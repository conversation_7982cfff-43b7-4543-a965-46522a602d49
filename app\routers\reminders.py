from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime
import base64
from pydantic import BaseModel

from .. import models, schemas
from ..database import get_db
from ..medication_ocr import MedicationOCRProcessor

router = APIRouter(
    prefix="/reminders",
    tags=["medication reminders"],
    responses={404: {"description": "Not found"}},
)

# Initialize OCR processor
medication_ocr_processor = MedicationOCRProcessor()

@router.post("/", response_model=schemas.MedicationReminder)
def create_reminder(
    reminder: schemas.MedicationReminderCreate,
    user_id: int,
    db: Session = Depends(get_db)
):
    """Create a new medication reminder manually."""
    # Verify the user exists
    user = db.query(models.User).filter(models.User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    db_reminder = models.MedicationReminder(
        user_id=user_id,
        name=reminder.name,
        dosage=reminder.dosage,
        schedule_datetime=reminder.schedule_datetime,
        schedule_dosage=reminder.schedule_dosage,
        notes=reminder.notes
    )
    db.add(db_reminder)
    db.commit()
    db.refresh(db_reminder)
    return db_reminder

@router.get("/{user_id}", response_model=List[schemas.MedicationReminder])
def get_user_reminders(
    user_id: int,
    skip: int = 0,
    limit: int = 100,
    include_taken: bool = True,
    db: Session = Depends(get_db)
):
    """Get all medication reminders for a user."""
    # Verify the user exists
    user = db.query(models.User).filter(models.User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    query = db.query(models.MedicationReminder).filter(models.MedicationReminder.user_id == user_id)
    
    if not include_taken:
        query = query.filter(models.MedicationReminder.is_taken == False)
    
    reminders = query.order_by(models.MedicationReminder.schedule_datetime).offset(skip).limit(limit).all()
    return reminders

@router.get("/reminder/{reminder_id}", response_model=schemas.MedicationReminder)
def get_reminder(reminder_id: int, db: Session = Depends(get_db)):
    """Get a specific medication reminder by ID."""
    reminder = db.query(models.MedicationReminder).filter(models.MedicationReminder.id == reminder_id).first()
    if not reminder:
        raise HTTPException(status_code=404, detail="Reminder not found")
    return reminder

@router.put("/reminder/{reminder_id}", response_model=schemas.MedicationReminder)
def update_reminder(
    reminder_id: int,
    reminder_update: schemas.MedicationReminderUpdate,
    db: Session = Depends(get_db)
):
    """Update a medication reminder."""
    db_reminder = db.query(models.MedicationReminder).filter(models.MedicationReminder.id == reminder_id).first()
    if not db_reminder:
        raise HTTPException(status_code=404, detail="Reminder not found")

    # Update fields if provided
    update_data = reminder_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_reminder, field, value)

    db.commit()
    db.refresh(db_reminder)
    return db_reminder

@router.delete("/reminder/{reminder_id}")
def delete_reminder(reminder_id: int, db: Session = Depends(get_db)):
    """Delete a medication reminder."""
    db_reminder = db.query(models.MedicationReminder).filter(models.MedicationReminder.id == reminder_id).first()
    if not db_reminder:
        raise HTTPException(status_code=404, detail="Reminder not found")

    db.delete(db_reminder)
    db.commit()
    return {"message": "Reminder deleted successfully"}

@router.post("/upload-prescription", response_model=schemas.MedicationOCRResponse)
async def upload_prescription_image(
    user_id: int = Form(...),
    image: UploadFile = File(...),
    notes: Optional[str] = Form(None),
    db: Session = Depends(get_db)
):
    """
    Upload an image of a medication prescription for OCR processing.
    Returns extracted medication data for user approval before saving.
    """
    # Verify the user exists
    user = db.query(models.User).filter(models.User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Validate file type
    if not image.content_type or not image.content_type.startswith("image/"):
        raise HTTPException(status_code=400, detail="File must be an image")

    try:
        # Read image data
        image_data = await image.read()

        # Process the image with OCR
        prescription_data = medication_ocr_processor.extract_prescription(image_data)

        if not prescription_data.get("name") and not prescription_data.get("schedule"):
            raise HTTPException(
                status_code=400, 
                detail="Could not extract medication information from the image. Please try a clearer image or add the reminder manually."
            )

        # Create the response with extracted data
        extracted_data = schemas.MedicationOCRExtraction(
            name=prescription_data.get("name", ""),
            dosage=prescription_data.get("dosage", ""),
            schedule=[
                schemas.MedicationScheduleItem(
                    datetime=item.get("datetime", ""),
                    dosage=item.get("dosage", "")
                ) for item in prescription_data.get("schedule", [])
            ],
            interpretation=prescription_data.get("interpretation", "")
        )

        return schemas.MedicationOCRResponse(
            extracted_data=extracted_data,
            total_reminders=len(prescription_data.get("schedule", [])),
            message="Medication information extracted successfully. Please review and approve to save."
        )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error processing prescription image: {str(e)}"
        )

class SaveOCRRemindersRequest(BaseModel):
    user_id: int
    extracted_data: schemas.MedicationOCRExtraction
    notes: Optional[str] = None

@router.post("/save-ocr-reminders")
def save_ocr_reminders(
    request: SaveOCRRemindersRequest,
    db: Session = Depends(get_db)
):
    """
    Save the OCR-extracted medication reminders after user approval.
    """
    # Extract data from request
    user_id = request.user_id
    extracted_data = request.extracted_data
    notes = request.notes

    # Verify the user exists
    user = db.query(models.User).filter(models.User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    try:
        saved_reminders = []

        for schedule_item in extracted_data.schedule:
            # Parse the datetime string
            try:
                datetime_str = schedule_item.datetime
                # Handle different datetime formats
                if datetime_str.endswith('Z'):
                    datetime_str = datetime_str.replace('Z', '+00:00')
                elif not datetime_str.endswith('+00:00') and 'T' in datetime_str and '+' not in datetime_str and 'Z' not in datetime_str:
                    # If it's ISO format without timezone, treat as local time
                    pass

                schedule_datetime = datetime.fromisoformat(datetime_str)
            except (ValueError, AttributeError) as e:
                # If parsing fails, skip this reminder
                print(f"Failed to parse datetime '{schedule_item.datetime}': {e}")
                continue

            db_reminder = models.MedicationReminder(
                user_id=user_id,
                name=extracted_data.name,
                dosage=extracted_data.dosage,
                schedule_datetime=schedule_datetime,
                schedule_dosage=schedule_item.dosage,
                notes=notes
            )
            db.add(db_reminder)
            saved_reminders.append(db_reminder)

        db.commit()
        
        # Refresh all saved reminders
        for reminder in saved_reminders:
            db.refresh(reminder)

        return {
            "message": f"Successfully saved {len(saved_reminders)} medication reminders",
            "reminders": [schemas.MedicationReminder.from_orm(reminder) for reminder in saved_reminders]
        }

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Error saving medication reminders: {str(e)}"
        )

@router.post("/mark-taken/{reminder_id}")
def mark_reminder_taken(
    reminder_id: int,
    db: Session = Depends(get_db)
):
    """Mark a medication reminder as taken."""
    db_reminder = db.query(models.MedicationReminder).filter(models.MedicationReminder.id == reminder_id).first()
    if not db_reminder:
        raise HTTPException(status_code=404, detail="Reminder not found")

    db_reminder.is_taken = True
    db.commit()
    db.refresh(db_reminder)
    
    return {"message": "Reminder marked as taken", "reminder": schemas.MedicationReminder.from_orm(db_reminder)}

@router.get("/upcoming/{user_id}")
def get_upcoming_reminders(
    user_id: int,
    hours: int = 24,
    db: Session = Depends(get_db)
):
    """Get upcoming medication reminders for a user within the specified hours."""
    # Verify the user exists
    user = db.query(models.User).filter(models.User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    from datetime import timedelta
    now = datetime.utcnow()
    future_time = now + timedelta(hours=hours)

    reminders = db.query(models.MedicationReminder).filter(
        models.MedicationReminder.user_id == user_id,
        models.MedicationReminder.is_taken == False,
        models.MedicationReminder.schedule_datetime >= now,
        models.MedicationReminder.schedule_datetime <= future_time
    ).order_by(models.MedicationReminder.schedule_datetime).all()

    return {"upcoming_reminders": [schemas.MedicationReminder.from_orm(reminder) for reminder in reminders]}
